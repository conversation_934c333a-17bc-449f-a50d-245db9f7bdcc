// Tipos para el sistema de gestión de cuestionarios administrativos

/**
 * Tipos de preguntas soportadas en el sistema
 */
export type TipoPregunta = 
  | 'abierta'
  | 'opcion_multiple'
  | 'verdadero_falso'
  | 'select'
  | 'checkbox'
  | 'radio_button'
  | 'escala_likert';

/**
 * Tipos de usuario que pueden ser asignados a cuestionarios
 */
export type TipoUsuario = 'alumno' | 'docente' | 'personal';

/**
 * Estados posibles de un cuestionario
 */
export type EstadoCuestionario = 'activo' | 'inactivo' | 'borrador';

/**
 * Estados de respuesta de un usuario a un cuestionario
 */
export type EstadoRespuesta = 'pendiente' | 'en_progreso' | 'completado';

/**
 * Configuración específica para cada tipo de pregunta
 */
export interface ConfiguracionPregunta {
  // Para preguntas abiertas
  limite_caracteres?: number;
  longitud_minima?: number;
  longitud_maxima?: number;
  
  // Para opciones múltiples, select, checkbox, radio
  opciones?: string[];
  seleccion_multiple?: boolean;
  permitir_otro?: boolean;
  opcion_por_defecto?: string;
  
  // Para checkbox
  minimo_selecciones?: number;
  maximo_selecciones?: number;
  
  // Para escala Likert
  puntos_escala?: number;
  etiqueta_minima?: string;
  etiqueta_maxima?: string;
  mostrar_numeros?: boolean;
}

/**
 * Estructura de una pregunta individual
 */
export interface Pregunta {
  id: string;
  tipo: TipoPregunta;
  texto: string;
  descripcion?: string;
  obligatoria: boolean;
  orden: number;
  configuracion: ConfiguracionPregunta;
  created_at?: string;
  updated_at?: string;
}

/**
 * Estructura principal de un cuestionario administrativo
 */
export interface CuestionarioAdmin {
  id: string;
  titulo: string;
  descripcion: string;
  preguntas: Pregunta[];
  tipos_usuario_asignados: TipoUsuario[];
  fecha_creacion: string;
  fecha_inicio?: string;
  fecha_fin?: string;
  estado: EstadoCuestionario;
  creado_por: string;
  creado_por_nombre?: string;
  total_preguntas: number;
  total_respuestas?: number;
  updated_at?: string;
}

/**
 * Datos para crear un nuevo cuestionario
 */
export interface CuestionarioAdminCreate {
  titulo: string;
  descripcion: string;
  preguntas: Omit<Pregunta, 'id' | 'created_at' | 'updated_at'>[];
  tipos_usuario_asignados: TipoUsuario[];
  fecha_inicio?: string;
  fecha_fin?: string;
  estado: EstadoCuestionario;
}

/**
 * Datos para actualizar un cuestionario existente
 */
export interface CuestionarioAdminUpdate {
  titulo?: string;
  descripcion?: string;
  preguntas?: Pregunta[];
  tipos_usuario_asignados?: TipoUsuario[];
  fecha_inicio?: string;
  fecha_fin?: string;
  estado?: EstadoCuestionario;
}

/**
 * Respuesta de un usuario a una pregunta específica
 */
export interface RespuestaPregunta {
  pregunta_id: string;
  valor: string | string[] | number;
  texto_otro?: string; // Para cuando se selecciona "Otro" en opciones
}

/**
 * Respuestas completas de un usuario a un cuestionario
 */
export interface RespuestaCuestionario {
  id?: string;
  cuestionario_id: string;
  usuario_id: number;
  respuestas: RespuestaPregunta[];
  estado: EstadoRespuesta;
  fecha_inicio?: string;
  fecha_completado?: string;
  tiempo_total_minutos?: number;
  progreso_porcentaje: number;
}

/**
 * Cuestionario asignado a un usuario con información de estado
 */
export interface CuestionarioAsignado {
  cuestionario: CuestionarioAdmin;
  respuesta?: RespuestaCuestionario;
  estado: EstadoRespuesta;
  fecha_asignacion: string;
  fecha_limite?: string;
  puede_responder: boolean;
}

/**
 * Filtros para la búsqueda de cuestionarios
 */
export interface FiltrosCuestionarios {
  titulo?: string;
  estado?: EstadoCuestionario;
  tipo_usuario?: TipoUsuario;
  fecha_creacion_desde?: string;
  fecha_creacion_hasta?: string;
  creado_por?: string;
  skip?: number;
  limit?: number;
}

/**
 * Estadísticas de un cuestionario
 */
export interface EstadisticasCuestionario {
  cuestionario_id: string;
  total_asignados: number;
  total_completados: number;
  total_en_progreso: number;
  total_pendientes: number;
  porcentaje_completado: number;
  tiempo_promedio_minutos?: number;
  fecha_ultima_respuesta?: string;
}

/**
 * Respuesta de la API para listado de cuestionarios
 */
export interface CuestionariosResponse {
  cuestionarios: CuestionarioAdmin[];
  total: number;
  skip: number;
  limit: number;
}

/**
 * Respuesta de la API para cuestionarios asignados a un usuario
 */
export interface CuestionariosAsignadosResponse {
  cuestionarios_asignados: CuestionarioAsignado[];
  total: number;
}

/**
 * Datos para asignación masiva de cuestionarios
 */
export interface AsignacionMasiva {
  cuestionario_ids: string[];
  tipos_usuario: TipoUsuario[];
  fecha_inicio?: string;
  fecha_fin?: string;
}

/**
 * Validación de pregunta
 */
export interface ValidacionPregunta {
  es_valida: boolean;
  errores: string[];
}

/**
 * Validación de cuestionario completo
 */
export interface ValidacionCuestionario {
  es_valido: boolean;
  errores_generales: string[];
  errores_preguntas: { [pregunta_id: string]: string[] };
}
